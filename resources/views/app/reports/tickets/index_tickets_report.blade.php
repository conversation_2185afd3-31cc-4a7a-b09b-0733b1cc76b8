@extends('layouts.app')

@section('content')
<x-report.index :title="__('permissions.get_tickets_report')" :route="route('reports.tickets')">
    <div class="row">
        <x-report.filter.field.date-from />
        <x-report.filter.field.date-to />
        <div class="col-sm-12 col-md-3 col-lg-3">
            <label class="mb-1" for="status">Status</label>
            <select class="form-control" id="status" name="status">
                <option value="">Todos</option>
                <option value="pending">Pendente</option>
                <option value="ongoing">Em andamento</option>
                <option value="resolved">Resolvido</option>
                <option value="cancelled">Cancelado</option>
            </select>
        </div>
        <div class="col-sm-12 col-md-3 col-lg-3">
            <label class="mb-1" for="team_id">Departamento</label>
            <select class="form-control" id="team_id" name="team_id">
                <option value="">Todas</option>
                {{-- Teams will be populated via AJAX or server-side --}}
            </select>
        </div>
    </div>
</x-report.index>
@endsection

@section('js-scripts')
<script>
    function resetFilters() {
        $('#date_from').val("{{ date('Y-m-d') }}");
        $('#date_to').val("{{ date('Y-m-d') }}");
        $('#status').val("");
        $('#team_id').val("");
    }
</script>
@endsection
