<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="pt">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-15">
    <title>OcMed - Chamados</title>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ global_asset('css/report-default.css') }}">
</head>

<body>
    <div>
        <div id="report-header" class="mb-2">
            <div class="float-left header">
                <p style="margin-top: 0; margin-bottom: 0;">Chamados</p>
                @if (!is_null($createdAtFrom))<p style="margin-top: 0; margin-bottom: 0;"><strong>Criado de:</strong> {{ $createdAtFrom->format('d/m/Y') }}</p>@endif
                @if (!is_null($createdAtTo))<p style="margin-top: 0; margin-bottom: 0;"><strong>Criado até:</strong> {{ $createdAtTo->format('d/m/Y') }}</p>@endif
                @if (!is_null($status))<p style="margin-top: 0; margin-bottom: 0;"><strong>Status:</strong> {{ $status }}</p>@endif
                @if (!is_null($ticketCategoryId))<p style="margin-top: 0; margin-bottom: 0;"><strong>Categoria:</strong> {{ $ticketCategoryId }}</p>@endif
                @if (!is_null($assignedToUserId))<p style="margin-top: 0; margin-bottom: 0;"><strong>Atribuído a:</strong> {{ $assignedToUserId }}</p>@endif
                @if (!is_null($createdByUserId))<p style="margin-top: 0; margin-bottom: 0;"><strong>Criado por:</strong> {{ $createdByUserId }}</p>@endif
                @if (!is_null($teamId))<p style="margin-top: 0; margin-bottom: 0;"><strong>Equipe:</strong> {{ $teamId }}</p>@endif
                <p style="margin-top: 0; margin-bottom: 0;"><strong>Emissão:</strong> {{ now()->setTimezone('-3:00')->format('d/m/Y - H:i:s') }}</p>
            </div>
            <div class="float-right">
                <img class="ctt-img-nav" src="{{ env('DIGITALOCEAN_SPACES_IMAGES_URL') . 'logo-' . tenant('id') . '.png' }}" alt="Logo" />
            </div>
        </div>

        <hr class="clear-both">

        <div id="report-body">
            <table class="table table-sm table-borderless table-striped">
                <thead>
                    <tr>
                        <th scope="col" style="text-align: left">ID</th>
                        <th scope="col" style="text-align: left">Categoria</th>
                        <th scope="col" style="text-align: left">Criado por</th>
                        <th scope="col" style="text-align: left">Atribuído a</th>
                        <th scope="col" style="text-align: left">Departamento</th>
                        <th scope="col" style="text-align: left">Título</th>
                        <th scope="col" style="text-align: left">Status</th>
                        <th scope="col" style="text-align: left">Criado em</th>
                    </tr>
                </thead>
                @foreach ($lines as $line)
                <tr>
                    <td scope="col" style="text-align: left">{{ $line['id'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['ticket_category_name'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['created_by_name'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['assigned_to_name'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['department_name'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['title'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['status_name'] }}</td>
                    <td scope="col" style="text-align: left">{{ $line['created_at'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
    </div>
</body>

</html>
