<?php

namespace App\Actions\Reports;

use App\Support\Dictionaries\ReportDictionary;
use Lorisleiva\Actions\Concerns\AsAction;

class GetReports
{
    use AsAction;

    /**
     * The reports array.
     *
     * @var array
     */
    protected $reports = [
        'management' => [
            'title' => 'Cadastros',
            'items' => [
                ReportDictionary::COMPANIES => 'Clientes',
                ReportDictionary::COMPANIES_WITH_LIFE_COUNT => 'Clientes com quantidade de vidas',
                ReportDictionary::PROVIDERS => 'Credenciados',
                ReportDictionary::PROVIDER_COMPANIES => 'Credenciados x clientes',
                ReportDictionary::PROVIDER_PROCEDURES => 'Exames dos credenciados',
                ReportDictionary::SUPPLIERS => 'Fornecedores',
            ]
        ],
        'contract' => [
            'title' => 'Contratos',
            'items' => [
                ReportDictionary::CONTRACTS => 'Contratos',
                ReportDictionary::CONTRACTS_WITHOUT_SERVICE_ORDERS => 'Contratos sem OS (+7 dias)',
            ]
        ],
        'entries' => [
            'title' => 'Lançamentos',
            'items' => [
                ReportDictionary::PROVIDER_EXPENSES => 'Despesas (credenciados)',
                ReportDictionary::SUPPLIER_EXPENSES => 'Despesas (fornecedores)',
            ]
        ],
        'billing' => [
            'title' => 'Faturamento',
            'items' => [
                ReportDictionary::PENDING_BILLING_CONTRACTS_FOR_PERIOD => 'Contratos pendentes de faturamento no período',
                ReportDictionary::COMMISSION_PAYMENT => 'Pagamento de comissões',
                ReportDictionary::COMPANY_LOSS_RATIO => 'Sinistralidade de exames por cliente',
                ReportDictionary::PROVIDER_LOSS_RATIO => 'Sinistralidade de exames por credenciado',
            ]
        ],
        'finance' => [
            'title' => 'Financeiro',
            'items' => [
                ReportDictionary::OPEN_RECEIVABLES => 'Contas a receber',
            ]
        ],
        'engineering' => [
            'title' => 'Engenharia',
            'items' => [
                ReportDictionary::SERVICE_ORDERS => 'Ordens de serviço',
            ]
        ],
        'tickets' => [
            'title' => 'Chamados',
            'items' => [
                ReportDictionary::TICKETS => 'Chamados',
            ]
        ]
    ];

    /**
     * Handle the action.
     *
     * @return mixed
     */
    public function handle(): mixed
    {
        $availableReports = [];

        foreach ($this->reports as $reportGroupName => $reportGroup) {
            foreach ($reportGroup['items'] as $reportNameKey => $reportNameValue) {
                if (!auth()->user()->can("get_{$reportNameKey}_report")) {
                    continue;
                }

                $availableReports[$reportGroup['title']][$reportNameKey] = $reportNameValue;
            }
        }

        return view('app.reports.index_reports', compact('availableReports'));
    }
}
