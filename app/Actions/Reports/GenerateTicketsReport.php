<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Jobs\OcMed\Reports\GenerateExcelTicketsReport;
use App\Models\Ticket;
use App\Support\Dictionaries\ReportDictionary;
use App\Support\Helpers\ReportHelper;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateTicketsReport implements Reportable
{
    use AsAction;

    public function asController(ActionRequest $request): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return LoadReportView::run(ReportDictionary::TICKETS);
        }

        $this->currentFormat = $request->getReportFormat();
        $this->excelFileName = 'contratos';
        $this->moduleName = Module::CONTRACT;
        $this->reportName = ReportDictionary::CONTRACTS;
        $this->reportExportClassName = ContractsExport::class;
        $this->notificationSubjectReportName = 'relatório de contratos';
        $this->notificationBodyReportEntity = 'contratos';

        try {
            $reportData = $this->handle(
                $request->input('format'),
                !is_null($request->input('date_from')) ? carbon($request->input('date_from')) : null,
                !is_null($request->input('date_to')) ? carbon($request->input('date_to')) : null,
                $request->input('status'),
                $request->input('team_id'),
            );

            if ($request->input('format') === ReportHelper::FORMAT_EXCEL) {
                GenerateExcelTicketsReport::dispatch(auth()->id(), $reportData)->onQueue(
                    config('queue.default_queue_names.reports'),
                );

                return redirect_success(
                    'reports.index',
                    'O relatório está sendo gerado e será enviado para o seu email ao final do processo.',
                );
            }

            if ($request->input('format') === ReportHelper::FORMAT_SCREEN) {
                return view('app.reports.tickets.tickets_report', $reportData);
            }

            return Pdf::loadView('app.reports.tickets.tickets_report', $reportData)
                ->setPaper('a4', 'landscape')
                ->stream();
        } catch (Throwable $th) {
            Log::error($th);
            return redirectError($th->getMessage());
        }
    }

    /**
     * Handle the action.
     *
     * @param  string $format
     * @param  \Carbon\Carbon|null $createdAtFrom
     * @param  \Carbon\Carbon|null $createdAtTo
     * @param  string|null $status
     * @param  int|null $ticketCategoryId
     * @param  int|null $assignedToUserId
     * @param  int|null $createdByUserId
     * @param  int|null $teamId
     * @return array
     */
    public function handle(
        string $format,
        ?Carbon $createdAtFrom = null,
        ?Carbon $createdAtTo = null,
        string $status = null,
        int $ticketCategoryId = null,
        int $assignedToUserId = null,
        int $createdByUserId = null,
        int $teamId = null,
    ): array {
        return ReportHelper::isScreenable($format)
            ? $this->buildPDFData(
                $createdAtFrom,
                $createdAtTo,
                $status,
                $ticketCategoryId,
                $assignedToUserId,
                $createdByUserId,
                $teamId,
            )
            : $this->buildExcelData(
                $createdAtFrom,
                $createdAtTo,
                $status,
                $ticketCategoryId,
                $assignedToUserId,
                $createdByUserId,
                $teamId,
            );
    }

    /**
     * @inheritDoc
     */
    public function buildPDFData(mixed ...$params): array
    {
        $createdAtFrom = $params[0];
        $createdAtTo = $params[1];
        $status = $params[2];
        $ticketCategoryId = $params[3];
        $assignedToUserId = $params[4];
        $createdByUserId = $params[5];
        $teamId = $params[6];

        $lines = $this->buildReportLines(
            $createdAtFrom,
            $createdAtTo,
            $status,
            $ticketCategoryId,
            $assignedToUserId,
            $createdByUserId,
            $teamId,
        );

        return [
            'createdAtFrom' => $createdAtFrom,
            'createdAtTo' => $createdAtTo,
            'status' => $status,
            'ticketCategoryId' => $ticketCategoryId,
            'assignedToUserId' => $assignedToUserId,
            'createdByUserId' => $createdByUserId,
            'teamId' => $teamId,
            'lines' => $lines,
        ];
    }

    /**
     * @inheritDoc
     */
    public function buildExcelData(mixed ...$params): array
    {
        $createdAtFrom = $params[0];
        $createdAtTo = $params[1];
        $status = $params[2];
        $ticketCategoryId = $params[3];
        $assignedToUserId = $params[4];
        $createdByUserId = $params[5];
        $teamId = $params[6];

        return $this->buildReportLines(
            $createdAtFrom,
            $createdAtTo,
            $status,
            $ticketCategoryId,
            $assignedToUserId,
            $createdByUserId,
            $teamId,
        );
    }

    /**
     * Build the report lines.
     *
     * @param  \Carbon\Carbon|null $createdAtFrom
     * @param  \Carbon\Carbon|null $createdAtTo
     * @param  string|null $status
     * @param  int|null $ticketCategoryId
     * @param  int|null $assignedToUserId
     * @param  int|null $createdByUserId
     * @param  int|null $teamId
     * @return array
     */
    public function buildReportLines(
        ?Carbon $createdAtFrom = null,
        ?Carbon $createdAtTo = null,
        string $status = null,
        int $ticketCategoryId = null,
        int $assignedToUserId = null,
        int $createdByUserId = null,
        int $teamId = null,
    ): array {
        return Ticket::query()
            ->with([
                'ticketCategory:id,name',
                'creationUser:id,name',
                'assignedUser:id,name',
                'team:id,name',
            ])
            ->when(!is_null($createdAtFrom), fn (Builder $query): Builder => $query->whereDate('created_at', '>=', $createdAtFrom))
            ->when(!is_null($createdAtTo), fn (Builder $query): Builder => $query->whereDate('created_at', '<=', $createdAtTo))
            ->when(!is_null($status), fn (Builder $query): Builder => $query->where('status', $status))
            ->when(!is_null($ticketCategoryId), fn (Builder $query): Builder => $query->where('ticket_category_id', $ticketCategoryId))
            ->when(!is_null($assignedToUserId), fn (Builder $query): Builder => $query->where('assigned_to_user_id', $assignedToUserId))
            ->when(!is_null($createdByUserId), fn (Builder $query): Builder => $query->where('created_by_user_id', $createdByUserId))
            ->when(!is_null($teamId), fn (Builder $query): Builder => $query->where('team_id', $teamId))
            ->get()
            ->map(function (Ticket $ticket) {
                return [
                    'id' => $ticket->id,
                    'ticket_category_name' => $ticket->ticketCategory->name ?? '',
                    'created_by_name' => $ticket->creationUser->name ?? '',
                    'assigned_to_name' => $ticket->assignedUser->name ?? '',
                    'department_name' => $ticket->team->name ?? '',
                    'title' => $ticket->summary,
                    'status_name' => $ticket->friendly_status,
                    'created_at' => $ticket->created_at->format('d/m/Y H:i:s'),
                ];
            })
            ->toArray();
    }
}
