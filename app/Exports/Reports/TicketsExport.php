<?php

namespace App\Exports\Reports;

use App\Core\Exports\BaseExport;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class TicketsExport extends BaseExport implements FromArray, WithHeadings, WithMapping
{
    public function __construct(private array $reportData)
    {

    }

    public function headings(): array
    {
        return [
            'ID',
            'Categoria',
            'Criado por',
            'Atribuído a',
            'Departamento',
            'Título',
            'Status',
            'Criado em',
        ];
    }

    public function array(): array
    {
        return $this->reportData;
    }

    /**
     * Map the row data.
     *
     * @param  array $ticket
     * @return array
     */
    public function map($ticket): array
    {
        return [
            $ticket['id'],
            $ticket['ticket_category_name'],
            $ticket['created_by_name'],
            $ticket['assigned_to_name'],
            $ticket['department_name'],
            $ticket['title'],
            $ticket['status_name'],
            $ticket['created_at'],
        ];
    }
}
