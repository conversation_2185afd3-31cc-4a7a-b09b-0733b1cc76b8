<?php

namespace App\Jobs\OcMed\Reports;

use App\Exports\Reports\TicketsExport;
use App\Models\User;
use App\Notifications\TicketsReportNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;

class GenerateExcelTicketsReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private int $userId, private array $reportData) {}

    public function handle()
    {
        $filename = 'reports/tickets/' . date('YmdHis') . '_chamados.xlsx';

        Excel::store(new TicketsExport($this->reportData), $filename);

        User::query()
            ->find($this->userId)
            ->notify(new TicketsReportNotification($filename));
    }
}
