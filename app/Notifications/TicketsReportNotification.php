<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TicketsReportNotification extends Notification
{
    use Queueable;

    public function __construct(private string $filename) {}

    public function via()
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject(config('app.name') . ' - relatório de chamados')
            ->greeting("Ol<PERSON>, {$notifiable->name}! Tudo bem?")
            ->line('O arquivo de chamados já foi gerado e encontra-se anexado a este email.')
            ->salutation('Agradecemos por utilizar os nossos serviços!')
            ->attach(storage_path("app/$this->filename"), [
                'as' => 'chamados.xlsx',
                'mime' => 'application/xlsx'
            ]);
    }
}
